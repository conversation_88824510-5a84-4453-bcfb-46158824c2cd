'use client';

import { useState, useEffect, useCallback } from 'react';

import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Tabs from '@mui/material/Tabs';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';
import CircularProgress from '@mui/material/CircularProgress';

import { paths } from 'src/routes/paths';
import { useParams, useRouter } from 'src/routes/hooks';

import { useChatbot } from 'src/actions/mooly-chatbot';

import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';

import ChatbotProductsTab from './chatbot-products-tab';
import ChatbotInstructionTab from './chatbot-instruction-tab';
import ChatbotFaqsTab from './chatbot-faqs-tab';
import ChatbotGeneralConfigTab from './chatbot-general-config-tab';

// ----------------------------------------------------------------------

export function ChatbotDetailView() {
  const params = useParams();
  const router = useRouter();
  const settings = useSettingsContext();
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);

  // Lấy ID chatbot từ URL
  const chatbotId = params.chatbotId;

  // Lấy thông tin chi tiết chatbot
  const { chatbot, isLoading, error, mutate: refreshChatbot } = useChatbot(chatbotId);

  // Xử lý thay đổi tab
  const handleChangeTab = useCallback((event, newValue) => {
    const botType = chatbot?.type || 'sale_bot';
    const maxTabs = botType === 'sale_bot' ? 3 : 4;

    // Chỉ cho phép chuyển tab nếu index hợp lệ
    if (newValue >= 0 && newValue < maxTabs) {
      setTabValue(newValue);
    }
  }, [chatbot?.type]);

  // Reset tab value khi chatbot được load lần đầu hoặc khi type thay đổi
  useEffect(() => {
    if (chatbot) {
      const botType = chatbot.type || 'sale_bot';
      const maxTabs = botType === 'sale_bot' ? 3 : 4;

      // Reset về 0 nếu tab hiện tại vượt quá số tab có sẵn
      if (tabValue >= maxTabs) {
        setTabValue(0);
      }
    }
  }, [chatbot?.type, tabValue]);

  // Đồng bộ tabValue khi chatbot type thay đổi
  useEffect(() => {
    if (chatbot?.type) {
      // Reset về tab đầu tiên khi type thay đổi
      setTabValue(0);
    }
  }, [chatbot?.type]);

  // Xử lý quay lại trang danh sách
  const handleBackToList = () => {
    router.push(paths.dashboard.moolyChatbot.chatbots);
  };

  // Xử lý làm mới dữ liệu
  const handleRefresh = () => {
    refreshChatbot();
  };

  // Lấy danh sách tabs theo loại bot
  const getTabsConfig = useCallback(() => {
    const botType = chatbot?.type || 'sale_bot';

    if (botType === 'sale_bot') {
      return [
        { label: 'Hướng dẫn', value: 0 },
        { label: 'Sản phẩm', value: 1 },
        { label: 'Cấu hình chung', value: 2 }
      ];
    } else {
      return [
        { label: 'Hướng dẫn', value: 0 },
        { label: 'Sản phẩm', value: 1 },
        { label: 'Câu hỏi thường gặp', value: 2 },
        { label: 'Cấu hình chung', value: 3 }
      ];
    }
  }, [chatbot?.type]);

  // Render tabs theo loại bot
  const renderTabs = () => {
    const tabsConfig = getTabsConfig();
    const maxTabs = tabsConfig.length;

    // Đảm bảo tabValue không vượt quá số tab có sẵn
    const currentTabValue = tabValue >= maxTabs ? 0 : tabValue;

    return (
      <Tabs
        value={currentTabValue}
        onChange={handleChangeTab}
        sx={{
          px: 2,
          bgcolor: 'background.neutral',
        }}
      >
        {tabsConfig.map((tab) => (
          <Tab key={tab.value} label={tab.label} />
        ))}
      </Tabs>
    );
  };

  // Render nội dung tab theo loại bot
  const renderTabContent = () => {
    const botType = chatbot?.type || 'sale_bot';
    const tabsConfig = getTabsConfig();
    const maxTabs = tabsConfig.length;

    // Đảm bảo tabValue không vượt quá số tab có sẵn
    const currentTabValue = tabValue >= maxTabs ? 0 : tabValue;

    if (botType === 'sale_bot') {
      // Sale bot: Sản phẩm, Hướng dẫn, Cấu hình chung
      switch (currentTabValue) {
        case 0:
          return <ChatbotInstructionTab chatbot={chatbot} />;
        case 1:
          return <ChatbotProductsTab chatbot={chatbot} />;
        case 2:
          return <ChatbotGeneralConfigTab chatbot={chatbot} onDataChange={refreshChatbot} />;
        default:
          return <ChatbotProductsTab chatbot={chatbot} />;
      }
    } else {
      // RAG bot: Hướng dẫn, Sản phẩm, Câu hỏi thường gặp, Cấu hình chung
      switch (currentTabValue) {
        case 0:
          return <ChatbotInstructionTab chatbot={chatbot} />;
        case 1:
          return <ChatbotProductsTab chatbot={chatbot} />;
        case 2:
          return <ChatbotFaqsTab chatbot={chatbot} />;
        case 3:
          return <ChatbotGeneralConfigTab chatbot={chatbot} onDataChange={refreshChatbot} />;
        default:
          return <ChatbotInstructionTab chatbot={chatbot} />;
      }
    }
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            Có lỗi xảy ra khi tải dữ liệu
          </Typography>
          <Button
            variant="contained"
            onClick={handleBackToList}
            startIcon={<Iconify icon="eva:arrow-back-fill" />}
          >
            Quay lại danh sách
          </Button>
        </Box>
      </Container>
    );
  }

  // Hiển thị thông báo nếu không tìm thấy chatbot
  if (!chatbot) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" paragraph>
            Không tìm thấy chatbot
          </Typography>
          <Button
            variant="contained"
            onClick={handleBackToList}
            startIcon={<Iconify icon="eva:arrow-back-fill" />}
          >
            Quay lại danh sách
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Button
            variant="outlined"
            color="inherit"
            onClick={handleBackToList}
            startIcon={<Iconify icon="eva:arrow-back-fill" />}
          >
            Quay lại
          </Button>
          <Typography variant="h4">Chi tiết Chatbot</Typography>
        </Stack>

        <Button
          variant="contained"
          color="primary"
          startIcon={<Iconify icon="solar:refresh-bold" />}
          onClick={handleRefresh}
        >
          Làm mới
        </Button>
      </Stack>

      <Card sx={{ mb: 3, p: 3 }}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems="center">
          <Avatar
            alt={chatbot.name}
            sx={{
              width: 80,
              height: 80,
              bgcolor: () => alpha(theme.palette.primary.main, 0.1),
              border: () => `solid 1px ${theme.palette.divider}`,
              fontSize: '2rem',
              fontWeight: 'bold',
            }}
          >
            {chatbot.name?.charAt(0).toUpperCase() || 'C'}
          </Avatar>

          <Stack spacing={1} flexGrow={1}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Typography variant="h4">{chatbot.name}</Typography>
              <BotTypeBadge botType={chatbot.type} />
            </Stack>

            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {chatbot.description || 'Không có mô tả'}
            </Typography>

            <Stack
              direction="row"
              alignItems="center"
              spacing={3}
              sx={{ typography: 'body2', color: 'text.secondary', mt: 1 }}
            >
              <Stack direction="row" alignItems="center" spacing={1}>
                <Iconify icon="solar:calendar-date-bold" width={16} />
                <span>Tạo ngày: {new Date(chatbot.createdAt).toLocaleDateString('vi-VN')}</span>
              </Stack>

              <Stack direction="row" alignItems="center" spacing={1}>
                <Iconify icon="solar:clock-circle-bold" width={16} />
                <span>Cập nhật: {new Date(chatbot.updatedAt).toLocaleDateString('vi-VN')}</span>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      </Card>

      <Card>
        {renderTabs()}

        <Box sx={{ p: 3 }}>
          {renderTabContent()}
        </Box>
      </Card>
    </Container>
  );
}
